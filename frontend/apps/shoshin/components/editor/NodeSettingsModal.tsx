"use client"

import { X } from "lucide-react"
import { useState } from "react"
import { useEditorStore } from "../../stores/editorStore"
import { Button } from "../ui/button"
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogHeader,
    DialogTitle,
} from "../ui/dialog"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Switch } from "../ui/switch"
import { Textarea } from "../ui/textarea"

interface NodeSettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  nodeId: string | null
}

export function NodeSettingsModal({
  open,
  onOpenChange,
  nodeId,
}: NodeSettingsModalProps) {
  const { nodes, setNodes } = useEditorStore()
  
  // Find the current node
  const currentNode = nodeId ? nodes.find(node => node.id === nodeId) : null
  
  // Local state for form fields
  const [label, setLabel] = useState(currentNode?.data?.label || "")
  const [description, setDescription] = useState(currentNode?.data?.description || "")
  const [enabled, setEnabled] = useState(currentNode?.data?.enabled ?? true)
  const [hasVerticalPorts, setHasVerticalPorts] = useState(currentNode?.data?.hasVerticalPorts ?? false)

  // Update local state when node changes
  useState(() => {
    if (currentNode) {
      setLabel(currentNode.data?.label || "")
      setDescription(currentNode.data?.description || "")
      setEnabled(currentNode.data?.enabled ?? true)
      setHasVerticalPorts(currentNode.data?.hasVerticalPorts ?? false)
    }
  })

  const handleSave = () => {
    if (!currentNode || !nodeId) return

    // Update the node in the store
    const updatedNodes = nodes.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            label,
            description,
            enabled,
            hasVerticalPorts,
          },
        }
      }
      return node
    })

    setNodes(updatedNodes)
    onOpenChange(false)
  }

  const handleCancel = () => {
    // Reset form to original values
    if (currentNode) {
      setLabel(currentNode.data?.label || "")
      setDescription(currentNode.data?.description || "")
      setEnabled(currentNode.data?.enabled ?? true)
      setHasVerticalPorts(currentNode.data?.hasVerticalPorts ?? false)
    }
    onOpenChange(false)
  }

  if (!currentNode) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col gap-0 overflow-hidden p-0 sm:max-w-[500px] [&>button]:hidden">
        <DialogHeader className="flex-shrink-0 border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-medium">
              Node Settings - {currentNode.data?.type || "Node"}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0"
              onClick={handleCancel}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-4">
          <div className="space-y-6">
            {/* Basic Settings */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-foreground">Basic Settings</h3>
              
              <div className="space-y-2">
                <Label htmlFor="node-label">Label</Label>
                <Input
                  id="node-label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                  placeholder="Enter node label"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="node-description">Description</Label>
                <Textarea
                  id="node-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter node description"
                  rows={3}
                />
              </div>
            </div>

            {/* Behavior Settings */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-foreground">Behavior</h3>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="node-enabled">Enabled</Label>
                  <p className="text-xs text-muted-foreground">
                    Whether this node is active in the workflow
                  </p>
                </div>
                <Switch
                  id="node-enabled"
                  checked={enabled}
                  onCheckedChange={setEnabled}
                />
              </div>

              <div className="flex items-center justify-between opacity-50">
                <div className="space-y-0.5">
                  <Label htmlFor="node-ports" className="text-muted-foreground">
                    Vertical Ports
                    <span className="ml-2 text-xs bg-muted px-2 py-0.5 rounded-full">Coming Soon</span>
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Use vertical connection ports instead of horizontal
                  </p>
                </div>
                <Switch
                  id="node-ports"
                  checked={hasVerticalPorts}
                  onCheckedChange={() => {}} // Disabled - coming soon
                  disabled={true}
                />
              </div>
            </div>

            {/* Node Info */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-foreground">Node Information</h3>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">Type</Label>
                  <p className="font-medium">{currentNode.data?.type || "Unknown"}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">ID</Label>
                  <p className="font-mono text-xs">{currentNode.id}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 border-t px-6 py-4">
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
